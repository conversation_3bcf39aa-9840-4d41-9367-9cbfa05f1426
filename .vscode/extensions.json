{
    "recommendations": [
        // 日志查阅相关扩展
        "emilast.LogFileHighlighter",      // 日志文件高亮 - 必装
        "berublan.vscode-log-viewer",      // 日志查看器 - 推荐
        "eriklynd.json-tools",             // JSON工具 - 用于格式化JSON日志
        "chrmarti.regex",                  // 正则表达式预览器 - 用于搜索日志
        
        // 通用开发扩展
        "ms-python.python",                // Python支持
        "ms-python.autopep8",              // Python格式化
        "esbenp.prettier-vscode",          // 代码格式化
        "ms-vscode.vscode-json",           // JSON语言支持
        
        // 可选的日志相关扩展
        "mechatroner.rainbow-csv",         // CSV彩色显示 - 如果需要导出CSV格式日志
        "ms-vscode.hexeditor",             // 十六进制编辑器 - 查看二进制日志文件
        "redhat.vscode-yaml"               // YAML支持 - 如果有YAML格式的配置文件
    ]
}
