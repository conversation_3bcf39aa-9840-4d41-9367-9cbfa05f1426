# -*- coding: utf-8 -*-
"""
review_and_refine.py

文档审核与优化模块 (v2.1 - 修复接口问题)
"""
import logging
import re
import json
from typing import Dict, List, Any, Optional
import uuid
from datetime import datetime  # 添加导入语句

import autogen
from .base import AutoGenBaseAgent
from ..data.db.database_manager import DatabaseManager
from ..config import settings


# 设置日志记录器，用于记录模块运行过程中的信息和错误
logger = logging.getLogger(__name__)

class DocumentRepository:
    """
    文档数据访问层（Repository模式）
    """
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def save_document(self, document_id, conversation_id, version, content, created_at, updated_at, status='draft'):
        """保存新版本文档到数据库，并返回新的文档ID"""
        try:
            insert_sql = """
                INSERT INTO documents (
                    document_id, conversation_id, version, content, status, 
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (document_id, conversation_id, version, content, status, created_at, updated_at)
            if len(params) != 7:
                raise ValueError(f"参数数量错误: 需要 7 个参数，但提供了 {len(params)} 个参数: {params}")
            
            await self.db_manager.execute_update(insert_sql, params)
            logger.info(f"新版本文档已保存，文档ID: {document_id}, 版本号: {version}")
            return document_id
        except Exception as e:
            logger.error(f"保存文档失败: {str(e)}")
            raise

    async def get_latest_document(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """获取指定会话的最新文档"""
        try:
            query = "SELECT * FROM documents WHERE conversation_id = ? ORDER BY version DESC LIMIT 1"
            document = await self.db_manager.get_record(query, (conversation_id,))
            return document
        except Exception as e:
            logger.error(f"获取最新文档失败: {e}")
            return None
            
class AutoGenReviewAndRefineAgent(AutoGenBaseAgent):
    """
    基于AutoGen框架的文档审核与优化代理
    """
    def __init__(self,
                 llm_client: Any,
                 llm_config: Dict[str, Any] = None,
                 agent_name: str = "review_and_refine",
                 **kwargs):
        
        super().__init__(name=agent_name, **kwargs)

        self.llm_client = llm_client
        self.agent_name = agent_name
        
        from ..utils.prompt_loader import PromptLoader
        self.prompt_loader = PromptLoader()

        db_path = llm_config.get("db_path") if llm_config else None
        self.db_manager = DatabaseManager(db_path or str(settings.DATABASE_PATH))
        self.document_repository = DocumentRepository(self.db_manager)


    async def process_message(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        【公开接口】处理文档修改请求的核心方法。
        
        Args:
            data (Dict[str, Any]): 包含 'text' (用户修改意见) 和 'session_id' 的字典。

        Returns:
            Dict[str, Any]: 包含操作结果和响应文本的字典。
        """
        modification_request = data.get("text")
        session_id = data.get("session_id")
        
        if not modification_request or not session_id:
            return {"success": False, "text_response": "处理修改请求时缺少必要信息。"}

        try:
            # 1. 获取最新的文档版本
            latest_doc = await self.document_repository.get_latest_document(session_id)
            if not latest_doc:
                logger.error(f"在处理修改请求时，未找到会话 {session_id} 的任何文档。")
                return {"success": False, "text_response": "抱歉，我没有找到需要修改的文档。"}

            original_content = latest_doc.get("content", "")
            document_version = latest_doc.get("version", 0)
            
            # 2. 调用LLM生成优化后的文档
            new_document_content = await self.generate_new_document(
                original_content,
                modification_request
            )
            if not new_document_content:
                return {"success": False, "text_response": "抱歉，根据您的建议生成新文档时出错。"}

            # 3. 将新版本的文档存入数据库
            new_document_id = await self.document_repository.save_document(
                document_id=f"doc_{str(uuid.uuid4()).replace('-', '')}",
                conversation_id=session_id,
                content=new_document_content,
                version=document_version + 1,
                created_at=datetime.now(),  # 使用 datetime.now()
                updated_at=datetime.now(),  # 使用 datetime.now()
                status='draft'  # 添加默认状态
            )

            if not new_document_id:
                return {"success": False, "text_response": "抱歉，保存修改后的文档时出错，请稍后再试。"}

            logger.info(f"文档修改成功，新版本文档ID: {new_document_id}")

            # 4. 构建成功响应
            response_text = "好的，我已经根据您的要求对文档进行了修改。请查看新版本：\n\n" + new_document_content             
            return {
                "success": True,
                "next_action": "review_again", # 指示总调度继续等待用户审阅
                "text_response": response_text,
                "modified_document": new_document_content
            }

        except Exception as e:
            self.logger.error(f"处理文档修改时发生严重错误: {e}", exc_info=True)
            return {"success": False, "text_response": "抱歉，处理您的修改请求时发生了内部错误。"}


    async def generate_new_document(self, current_document: str, feedback: str) -> str:
        """
        基于用户反馈生成优化后的文档内容
        """
        try:
            prompt = self.prompt_loader.load_prompt(
                "review_and_refine",
                {
                    "current_document": current_document,
                    "user_feedback": feedback
                }
            )

            response = await self.llm_client.call_llm(
                messages=[{"role": "user", "content": prompt}],
                agent_name="review_and_refine",
                temperature=0.5,
                max_tokens=4096
            )

            content = response.get("content", "")
            if not content:
                logger.error("LLM为文档优化返回了空内容")
                return ""
            
            # 尝试从返回内容中提取JSON
            if "{" in content and "}" in content:
                # 假设LLM可能返回被解释性文字包裹的JSON
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    try:
                        # 假设文档内容在某个字段中
                        parsed_json = json.loads(json_match.group(0))
                        # 根据实际返回的JSON结构调整这里的key
                        return parsed_json.get("modified_content", str(parsed_json))
                    except json.JSONDecodeError:
                        logger.warning("LLM返回了看似JSON但无法解析的内容，将直接使用整个字符串。")
                        return content.strip()
                    
            # 新增：移除Markdown代码块标记
            if isinstance(content, str): #确保content是字符串类型
                if content.startswith("```markdown\n") and content.endswith("\n```"):
                    content = content[len("```markdown\n"):-len("\n```")]
                elif content.startswith("```") and content.endswith("```"): # 更通用的移除
                    # 移除首尾的 ```
                    content = content[3:-3]
                    # 如果紧接着是 "markdown\n"，也移除它
                    if content.startswith("markdown\n"):
                        content = content[len("markdown\n"):]
                content = content.strip() # 清理可能残留的空白

            return content.strip()

        except Exception as e:
            self.logger.error(f"调用LLM生成新文档失败: {str(e)}", exc_info=True)
            return ""
