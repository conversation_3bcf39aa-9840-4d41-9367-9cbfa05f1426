根据用户的反馈修改需求文档相应内容, 修改完成后按照原有格式输出 markdown 格式。

## 当前文档
{current_document}

## 用户反馈
{user_feedback}

## 步骤

1.  **理解任务：** 仔细分析用户反馈，确定所要求的具体更改。
2.  **定位相关内容：** 在提供的文档中找到需要修改的相应部分或内容。
3.  **应用更改：** 根据用户反馈实施更改，确保准确、清晰和完整。
4.  **保持结构和格式：** 保持原始文档的结构、格式和样式。
5.  **解决歧义：** 如果用户反馈不明确，提出澄清问题，以更好地理解用户的意图。
6.  **输出修改后的文档：** 以 markdown 格式输出修改后的文档，不包含任何额外的解释或评论。

# 输出格式

输出应为完整的 markdown 文档，反映用户反馈中要求的更改。 该文档应保持原始结构和格式。


# 注意事项
密切注意用户反馈中的具体说明。
确保修改后的文档没有错误和不一致之处。
如果用户反馈要求进行重大更改，从而改变文档的整体范围或目的，请考虑向用户寻求进一步的澄清。