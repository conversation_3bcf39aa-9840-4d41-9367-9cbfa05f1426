根据用户的反馈修改需求文档相应内容。

## 当前文档
{current_document}

## 用户反馈
{user_feedback}

## 处理步骤

1.  **理解任务：** 仔细分析用户反馈，确定所要求的具体更改。
2.  **判断可行性：** 评估是否能够理解用户的修改意图。
3.  **处理方式选择：**
    - 如果能够清楚理解用户意图：直接执行修改
    - 如果用户反馈模糊或有歧义：提出澄清问题
4.  **执行修改：** 根据用户反馈实施更改，确保准确、清晰和完整。
5.  **保持格式：** 保持原始文档的结构、格式和样式。

## 输出格式

**如果能够理解并执行修改：**
直接输出完整的修改后的 markdown 文档，不包含任何额外说明。

**如果需要澄清：**
输出澄清问题，格式如下：
```
需要澄清：[具体的澄清问题]

例如：
- 您希望修改哪个具体部分？
- 您希望将"XXX"修改为什么内容？
- 您是希望添加新内容还是替换现有内容？
```

## 处理原则

1. **优先尝试理解和执行**：即使用户表达不够精确，也要尝试理解其意图并执行修改
2. **常见修改类型识别**：
   - "修改XXX" → 找到XXX相关内容并优化
   - "删除XXX" → 移除XXX相关内容
   - "添加XXX" → 在合适位置添加XXX内容
   - "调整XXX" → 优化XXX部分的表述
3. **只在真正无法理解时才澄清**：避免过度澄清影响用户体验
4. **保持文档完整性**：确保修改后的文档结构完整、格式正确