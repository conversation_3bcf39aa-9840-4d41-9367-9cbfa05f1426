# Python相关
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.python-version
.coverage
.coverage.*
htmlcov/
coverage/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Python虚拟环境
venv/
env/
.env/
.venv/
.venv_autogen/
ENV/
virtualenv/

# 分发/打包相关
*.egg
*.egg-info/
dist/
build/
eggs/
parts/
bin/
var/
sdist/
develop-eggs/
.installed.cfg
lib/
lib64/

# 日志文件
logs/
*.log
*.log.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 数据库文件
*.db
*.sqlite
*.sqlite3
aidatabase.db
sessions.db
*.rdb

# IDE和编辑器
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/tasks.json
.idea/
*.swp
*.swo
*.pyedit
*_pyedit
*.pyerror
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统
.DS_Store
Thumbs.db
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini

# Node.js 相关
node_modules/
dist/
dist-ssr/
.cache/
.parcel-cache/

# 前端构建输出
.output/
.nuxt/
.next/
out/
build/
.svelte-kit/
.vercel/

# 前端开发工具
.turbo/
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# 前端测试和覆盖率
coverage/
.nyc_output/
cypress/videos/
cypress/screenshots/
reports/
*.lcov

# 前端依赖锁定文件
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb

# 本地配置文件
.env
.env.local
.env.*.local
*.local.yaml
*.local.yml
*.local.json
config.local.*

# 临时文件
*.tmp
*.temp
.temp/
.tmp/
tmp/
temp/

# 其他前端相关
.storybook/
storybook-static/
*.stories.js
*.stories.ts
*.stories.jsx
*.stories.tsx
.vitepress/cache/
.vitepress/dist/

# AI生成的临时文件和备份
*_backup/
*_temp/
.ai_generated/

# Jupyter Notebook
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# VS Code Workspace 特定文件
*.code-workspace
.history/
.ionide/

# 其他
*.bak
*.orig
*.rej
*.old
.roo/
.trae/

# macOS特定文件
.AppleDouble
.LSOverride
Icon
._*

# Visual Studio Code 工作区特定文件
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/tasks.json
tests/quick_session_test.py
tests/test_session_recovery.py
